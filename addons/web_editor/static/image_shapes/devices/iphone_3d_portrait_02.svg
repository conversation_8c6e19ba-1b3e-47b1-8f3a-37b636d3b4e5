<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 1370 1960" data-forced-size="true" width="1370" height="1960" data-img-aspect-ratio="9:19.5" data-img-perspective="[[40.43, 4.14], [95.93, 1.13], [55.37, 96.89], [0.02, 88.08]]">
    <defs>
        <linearGradient id="gradient_01" x1="384.7" y1="207.5" x2="1041.87" y2="1723.96" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.46" stop-color="#8a8a8a"/>
            <stop offset="0.48" stop-color="#a1a1a1"/>
            <stop offset="0.51" stop-color="#8a8a8a"/>
            <stop offset="0.56" stop-color="#787878"/>
            <stop offset="0.87" stop-color="#646464"/>
            <stop offset="0.93" stop-color="#414141"/>
            <stop offset="0.97" stop-color="#2c2c2c"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="light_adjust" x1="384.7" y1="207.5" x2="1041.87" y2="1723.96" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="0.5" stop-color="#fff" stop-opacity=".5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <radialGradient id="gradient_02" cx="822.33" cy="82.6" r="8.05" gradientTransform="translate(390.03 -621.79) rotate(52.73)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#658088"/>
            <stop offset="0.07" stop-color="#4f6571"/>
            <stop offset="0.16" stop-color="#374756"/>
            <stop offset="0.27" stop-color="#232d40"/>
            <stop offset="0.39" stop-color="#131a2f"/>
            <stop offset="0.52" stop-color="#080c23"/>
            <stop offset="0.69" stop-color="#02041c"/>
            <stop offset="1" stop-color="#00021a"/>
        </radialGradient>
        <linearGradient id="gradient_03" x1="23526.71" y1="13575.85" x2="23604.87" y2="13575.85" gradientTransform="translate(24781.2 14165.22) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#141414"/>
            <stop offset="0.35" stop-color="#343434"/>
            <stop offset="0.5" stop-color="#424242"/>
            <stop offset="0.65" stop-color="#343434"/>
            <stop offset="1" stop-color="#141414"/>
        </linearGradient>
        <linearGradient id="gradient_04" x1="15898.35" y1="11343.3" x2="15908.83" y2="11316.22" gradientTransform="matrix(-1.46, 0.58, -0.77, -1.93, 32347.6, 14435.69)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <linearGradient id="gradient_05" x1="23427.56" y1="13969.92" x2="23490.77" y2="13986.27" gradientTransform="translate(24781.2 14165.22) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.65" stop-color="#0d0d0d"/>
            <stop offset="1" stop-color="#5c5c5c"/>
        </linearGradient>
        <linearGradient id="gradient_06" x1="23889.45" y1="12397.07" x2="23949.46" y2="12418.73" gradientTransform="translate(24781.2 14165.22) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.62" stop-color="#0d0d0d"/>
            <stop offset="1" stop-color="#5c5c5c"/>
        </linearGradient>
        <linearGradient id="gradient_07" x1="24671.82" y1="12349.53" x2="24671.82" y2="12384.88" gradientTransform="translate(24781.2 14165.22) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.62" stop-color="#1f1f1f"/>
            <stop offset="1" stop-color="#c4c4c4"/>
        </linearGradient>
        <linearGradient id="gradient_08" x1="23386.74" y1="13700.71" x2="24565.31" y2="13226.67" gradientTransform="translate(24781.2 14165.22) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="782.94 52.83 616.21 69.77 558.17 105.14 520.58 155.81 25.56 1625.62 19.02 1695.09 65.88 1760.2 666.27 1893.42 772.31 1854.74 816.45 1758.25 1301.11 105.96 1278.65 25.7 1209.99 19.32 782.94 52.83"/>
        </clipPath>
        <path id="filterPath" d="M0.8832,0.0099l0.0501,0.0033L0.9497,0.0541,0.5959,0.8971l-0.0322,0.0492-0.0774,0.0197L0.0481,0.8981,0.0139,0.8648l0.0048-0.0354,0.3613-0.7499,0.0274-0.0259,0.0423-0.018L0.5715,0.027Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1341.57,48.7l-39-31.3s-31.2-20.7-81.4-16.9c-50,3.8-414.8,34.4-548.8,45-63.8,5-137.7,31.2-168.7,118.5-33.8,95.3-483.3,1422.7-492.4,1450.3s-28.7,103.1,23.6,150.6,64.8,49.4,71.6,51.2,527.4,128.9,554.6,134.7,167.9,56.5,230-160,469.6-1619,475.3-1641.9S1375.37,74.1,1341.57,48.7Zm-57.8,74.5c-7.1,30.5-443.7,1499.8-485.7,1643.6s-142.6,108.3-172.6,102.3-475.4-107.8-540.8-123.6-61-84.5-49.6-120.9S499.77,241.9,524.87,169.1s88.8-93.2,128.8-95.1c28.2-1.4,104.5-8.2,113-8.6,8.5-.6,19.3-2.7,15.8,12.7-6.2,27.6,13.8,37.6,27.4,36.9,15.5-.9,128.5-8.6,171.4-10.4s58.5-35.8,63.6-48.5,12.2-12.9,18.2-13.4,62.3-5.6,148.8-11.6S1290.87,92.7,1283.77,123.2Z" fill="url(#gradient_01)"/>
        <path d="M1341.57,48.6l-39-31.3s-31.3-20.7-81.4-16.9-414.8,34.5-548.8,45c-63.8,5-137.7,31.3-168.7,118.5-33.8,95.3-483.3,1422.7-492.39,1450.3s-28.71,103.1,23.59,150.6,64.7,49.4,71.6,51.2,527.5,128.9,554.6,134.7,167.9,56.5,230-160,469.6-1619,475.3-1641.9S1375.37,74,1341.57,48.6ZM814.21,76.42c4.71-6.2,12.17-8.46,16.65-5s4.3,11.21-.42,17.4-12.17,8.46-16.65,5S809.49,82.61,814.21,76.42ZM1297.13,116c-7.1,30.5-452.71,1514.84-494.71,1658.64S659.82,1883,629.82,1877s-486.75-113.22-552.15-129-61-84.5-49.6-120.9S495.56,237.68,520.66,164.88s88.8-93.2,128.8-95.1c28.3-1.4,113.34-7.85,121.84-8.25,8.5-.6,19.3-2.7,15.8,12.7-6.2,27.6,11.16,37.92,24.76,37.22,15.5-.9,128.5-8.6,171.4-10.4s50.66-37.33,55.76-50,12.2-12.9,18.2-13.4,81.51-7.7,168-13.7S1304.23,85.52,1297.13,116Z" fill="url(#light_adjust)" opacity="0.2"/>
        <path d="M34.77,1765c10,8.8,24,16.2,42.1,20.7,100.3,24.8,540.4,132.3,559.2,135.4,72.5,11.8,164.5-.7,202.5-128.6,40.5-136.6,477.5-1621.2,485.1-1651,5.3-20.4,20.2-82.4-11.39-116.3l-9.7-7.8s-31.4-20.7-81.4-16.9-414.8,34.4-548.9,45c-63.8,5-137.7,31.2-168.7,118.5-33.8,95.4-483.3,1422.7-492.4,1450.3S-17.43,1717.4,34.77,1765Zm-10.7-169.9c14.7-41.9,448-1330,481.9-1427.4C537.47,77,622.77,56,649.77,54.1c19.7-1.4,442.7-35.7,567.4-45.5s93.6,101.8,86,131.2-439.3,1496.3-479.4,1631.4c-37.6,126.4-121,140.5-192.6,128.9-18.7-3.1-454.1-106.2-553.2-130.7S9.47,1637,24.07,1595.1Z" fill="#fff" opacity="0.5"/>
        <path d="M73.37,1784.7c91.8,22.6,545.2,133.5,564.2,136.6a297.42,297.42,0,0,0,42.3,3.2c27.9,0,53-5.8,74.8-17.2,39.9-20.9,69-61.1,86.2-119.3,30.5-102.5,474.5-1610.2,482.5-1640.9l.5-1.9s8-35.3,8-62.2c0-38.8-18.8-57.3-18.8-57.3s16.6,16.4,16.8,57.5c.1,17.5-9.8,61-9.8,61l-.5,1.9c-8,30.7-452,1538.2-482.5,1640.8-17,57.2-45.4,96.6-84.3,116.9-30.3,16-70,21.1-112.4,14.2-18.4-3-460.3-111.2-568.6-137.7-14.9-3.7-27.4-8.8-36.7-16.4l-4.9-3.5h0c18.2,19.1,43.2,24.3,43.2,24.3Z" fill="#515151"/>
        <path d="M1217.17,8.7c-124.7,9.8-547.8,44-567.3,45.4C622.87,56,537.57,77,506,167.7c-33.9,97.4-467.2,1385.5-481.9,1427.4S-21,1744.8,78,1769.4s534.5,127.6,553.2,130.7c71.6,11.6,155-2.5,192.6-128.9,40.1-135.1,471.8-1601.9,479.4-1631.3S1341.87-1.1,1217.17,8.7Zm66.6,114.5c-7.1,30.5-443.7,1499.8-485.7,1643.6s-142.6,108.3-172.6,102.3-475.4-107.8-540.8-123.6-61-84.5-49.6-120.9S499.77,241.9,524.87,169.1s88.8-93.2,128.8-95.1c28.2-1.4,104.5-8.2,113-8.6,8.5-.6,19.3-2.7,15.8,12.7-6.2,27.6,13.8,37.6,27.4,36.9,15.5-.9,128.5-8.6,171.4-10.4s58.5-35.8,63.6-48.5,12.2-12.9,18.2-13.4,62.3-5.6,148.8-11.6S1290.87,92.7,1283.77,123.2Z"/>
        <g id="details">
            <g>
                <ellipse cx="822.32" cy="82.59" rx="14.1" ry="10.2" transform="translate(258.57 686.93) rotate(-52.73)" fill="#131516"/>
                <ellipse cx="822.33" cy="82.6" rx="9.2" ry="6.7" transform="matrix(0.61, -0.8, 0.8, 0.61, 258.57, 686.94)" fill="url(#gradient_02)"/>
            </g>
            <g>
                <path d="M1036.57,1166.2c2.2-6.2,5.8-18.9,14.9-16s7.8,12.7,5.6,22.1-41.2,140.5-53.7,188.2c-1.8,6.8-6.5,22.4-15.9,21.6-10.69-.9-7.69-20.3-5.69-26.9C995.87,1307.9,1034.47,1172.4,1036.57,1166.2Z" fill="none" stroke="#0d0d0d" stroke-miterlimit="10" stroke-width="1"/>
            </g>
            <g>
                <path d="M1195.07,694c-4,1.8-5.5,1.8-7.5,1.8a6.09,6.09,0,0,1-2.8-.6,7.38,7.38,0,0,1-2.5-1.8h0a21.38,21.38,0,0,1-5-21.2l53.3-181.4a15.81,15.81,0,0,1,1.9-4.1,6.21,6.21,0,0,1,2.7-2.1,17.73,17.73,0,0,1,4.2-1.2c.6-.1,1.3-.3,2.1-.4,1.2-.2,3.8.3,5.7.4,1.2.2.8.2,2,1.9a25.42,25.42,0,0,1,3.3,6.6,31.39,31.39,0,0,1,1.9,7.9,19,19,0,0,1-.4,6.9h0L1195.07,694" fill="url(#gradient_03)"/>
                <path d="M1201.57,685.9a10.42,10.42,0,0,1-6,7.1,6.55,6.55,0,0,1-7.5-1.8c-4.4-5-5.7-12.9-3.5-20.5l52.8-179.6a10.29,10.29,0,0,1,6.7-6.9,7.39,7.39,0,0,1,8.1,1.9c4.4,4.9,4.61,12.5,2.3,20.1Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.71"/>
            </g>
            <g>
                <path d="M574.37,1919.2c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C574.17,1914.9,572.57,1916.6,574.37,1919.2Z" fill="#000102"/>
                <path d="M581.77,1924.3l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A9,9,0,0,1,581.77,1924.3Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M584.67,1920l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M577.07,1919.7c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S575.57,1917.5,577.07,1919.7Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M599,1925c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C598.77,1920.7,597.17,1922.4,599,1925Z" fill="#000102"/>
                <path d="M606.37,1930.1l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A9,9,0,0,1,606.37,1930.1Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M609.27,1925.8l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M601.67,1925.5c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S600.17,1923.3,601.67,1925.5Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M622.57,1931.6c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C622.37,1927.3,620.77,1929,622.57,1931.6Z" fill="#000102"/>
                <path d="M630,1936.7l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A9,9,0,0,1,630,1936.7Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M632.87,1932.4l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M625.27,1932.1c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S623.77,1929.9,625.27,1932.1Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M549.77,1912.8c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C549.57,1908.5,548.07,1910.2,549.77,1912.8Z" fill="#000102"/>
                <path d="M557.17,1917.9l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A9.85,9.85,0,0,1,557.17,1917.9Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M560.17,1913.6l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M552.47,1913.3c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S551.07,1911.1,552.47,1913.3Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M524.17,1906.6c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C524,1902.3,522.37,1904,524.17,1906.6Z" fill="#000102"/>
                <path d="M531.57,1911.7l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A9,9,0,0,1,531.57,1911.7Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M534.47,1907.4l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M526.77,1907.1c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S525.37,1904.9,526.77,1907.1Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M500,1900.2c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C499.77,1895.9,498.17,1897.6,500,1900.2Z" fill="#000102"/>
                <path d="M507.27,1905.3l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A9.85,9.85,0,0,1,507.27,1905.3Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M510.27,1901l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M502.57,1900.7c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S501.17,1898.5,502.57,1900.7Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M430.37,1883.7c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C430.17,1879.4,428.57,1881.1,430.37,1883.7Z" fill="#000102"/>
                <path d="M437.77,1888.9l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A22.88,22.88,0,0,1,437.77,1888.9Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M440.77,1884.6l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M433.07,1884.2c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S431.57,1882.1,433.07,1884.2Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M282.17,1848.2c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C282,1843.9,280.37,1845.6,282.17,1848.2Z" fill="#000102"/>
                <path d="M289.57,1853.4l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7C291.47,1854,290.47,1853.7,289.57,1853.4Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M292.57,1849.1l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M284.87,1848.7c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.69-4.5-9-5S283.37,1846.6,284.87,1848.7Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M218.37,1833c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1C218.17,1828.7,216.57,1830.4,218.37,1833Z" fill="#000102"/>
                <path d="M225.77,1838.2l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A22.88,22.88,0,0,1,225.77,1838.2Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M228.67,1833.9l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M221.07,1833.5c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S219.57,1831.4,221.07,1833.5Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M196,1827.3c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1S194.17,1824.8,196,1827.3Z" fill="#000102"/>
                <path d="M203.27,1832.4l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7A17.15,17.15,0,0,1,203.27,1832.4Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M206.27,1828.1l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M198.57,1827.7c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5C198.47,1824.3,197.17,1825.7,198.57,1827.7Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M174.47,1822.3c1.7,2.6,6.2,5.3,10.2,6,4.2.7,6.2-.9,4.3-3.7s-6.9-5.6-10.9-6.1S172.67,1819.7,174.47,1822.3Z" fill="#000102"/>
                <path d="M181.77,1827.4l7.1-2.9c1.9,2.9-.1,4.5-4.3,3.7C183.67,1828,182.77,1827.7,181.77,1827.4Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M184.77,1823.2l2.4,1-2.6,4.1a18.89,18.89,0,0,1-6.3-2.5Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M177.07,1822.8c1.4,2.1,5.2,4.3,8.5,4.9,3.5.6,5.1-.7,3.6-3s-5.7-4.5-9-5S175.67,1820.7,177.07,1822.8Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M395.37,1880.8,339,1867.5c-7.39-1.8-14.19-5.7-17-9.6s1.9-6.1,8.81-4.5l50.6,11.8a48,48,0,0,1,19,9.5C405.57,1879.3,403.17,1882.6,395.37,1880.8Z" fill="#3d3d3d"/>
                <path d="M398.47,1881.1l-59.2-13.9c-7.2-1.6-13.8-5.4-16.5-9.2s1.8-5.9,8.5-4.3l53.3,12.4a45.82,45.82,0,0,1,18.6,9.2C408.27,1879.8,406,1883,398.47,1881.1Z" fill="url(#gradient_04)"/>
                <path d="M396.77,1880.4l-55.2-13.2c-6.3-1.5-12.2-4.9-14.7-8.3s1.4-5.3,7.5-3.9l50.2,11.9a41.9,41.9,0,0,1,16.6,8.3C405.67,1879.2,403.57,1882,396.77,1880.4Z" fill="#131313"/>
            </g>
            <polygon points="1348.47 210.5 1312.08 181.9 1291.38 179.4 1295.28 165.9 1315.47 168.9 1351.78 198.7 1348.47 210.5" fill="url(#gradient_05)"/>
            <polygon points="891.77 1787 850.67 1750.2 831.58 1743.4 835.67 1729.5 854.88 1736 895.38 1774 891.77 1787" fill="url(#gradient_06)"/>
            <path d="M132.77,1822.5l-29.3-27.1s-8.7-9.8,0-19.8l-14.3-3.5s-8.8,10.4,2.9,20.7l27.8,26.6Z" fill="url(#gradient_07)"/>
        </g>
        <path d="M1341.57,48.6l-39-31.3s-31.3-20.7-81.4-16.9-414.8,34.5-548.8,45c-63.8,5-137.7,31.3-168.7,118.5-33.8,95.3-483.3,1422.7-492.39,1450.3s-28.71,103.1,23.59,150.6,64.7,49.4,71.6,51.2,527.5,128.9,554.6,134.7,167.9,56.5,230-160,469.6-1619,475.3-1641.9S1375.37,74,1341.57,48.6ZM814.21,76.42c4.71-6.2,12.17-8.46,16.65-5s4.3,11.21-.42,17.4-12.17,8.46-16.65,5S809.49,82.61,814.21,76.42ZM1297.13,116c-7.1,30.5-452.71,1514.84-494.71,1658.64S659.82,1883,629.82,1877s-486.75-113.22-552.15-129-61-84.5-49.6-120.9S495.56,237.68,520.66,164.88s88.8-93.2,128.8-95.1c28.3-1.4,113.34-7.85,121.84-8.25,8.5-.6,19.3-2.7,15.8,12.7-6.2,27.6,11.16,37.92,24.76,37.22,15.5-.9,128.5-8.6,171.4-10.4s50.66-37.33,55.76-50,12.2-12.9,18.2-13.4,81.51-7.7,168-13.7S1304.23,85.52,1297.13,116Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
        <path d="M1211.77,31.1c-86.5,6-142.8,11.1-148.8,11.6s-13.1.7-18.2,13.4-20.7,46.7-63.6,48.5-155.9,9.5-171.4,10.4c-13.6.7-33.6-9.3-27.4-36.9,3.5-15.4-7.3-13.3-15.8-12.7-8.5.4-84.7,7.2-113,8.6-40,1.9-103.7,22.3-128.8,95.1-16.06,46.59-207.5,614.73-346.26,1027.35,335.23-318.64,844.21-760,1071.51-957.27,20.12-68.48,32.51-111,33.65-116C1290.77,92.7,1298.27,25.2,1211.77,31.1Z" opacity="0.4" fill="url(#gradient_08)"/>
    </g>
</svg>
